using Microsoft.EntityFrameworkCore;
using ITAssetAPI.Data;
using ITAssetAPI.Models;
using ITAssetAPI.DTOs;

namespace ITAssetAPI.Services
{
    public class TicketService
    {
        private readonly ApplicationDbContext _context;

        public TicketService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<TicketListDto>> GetUserTicketsAsync(int userId)
        {
            var tickets = await _context.Tickets
                .Where(t => t.UserId == userId)
                .OrderByDescending(t => t.CreatedDate)
                .Select(t => new TicketListDto
                {
                    Id = t.Id,
                    Title = t.Title,
                    Status = t.Status.ToString(),
                    Priority = t.Priority.ToString(),
                    Category = t.Category.ToString(),
                    UserName = t.UserName,
                    AssignedToName = t.AssignedToName,
                    CreatedDate = t.CreatedDate,
                    UpdatedDate = t.UpdatedDate
                })
                .ToListAsync();

            return tickets;
        }

        public async Task<List<TicketListDto>> GetAllTicketsAsync()
        {
            var tickets = await _context.Tickets
                .OrderByDescending(t => t.CreatedDate)
                .Select(t => new TicketListDto
                {
                    Id = t.Id,
                    Title = t.Title,
                    Status = t.Status.ToString(),
                    Priority = t.Priority.ToString(),
                    Category = t.Category.ToString(),
                    UserName = t.UserName,
                    AssignedToName = t.AssignedToName,
                    CreatedDate = t.CreatedDate,
                    UpdatedDate = t.UpdatedDate
                })
                .ToListAsync();

            return tickets;
        }

        public async Task<TicketDto?> GetTicketByIdAsync(int id)
        {
            var ticket = await _context.Tickets
                .Include(t => t.Comments)
                .Include(t => t.Attachments)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (ticket == null)
                return null;

            return new TicketDto
            {
                Id = ticket.Id,
                Title = ticket.Title,
                Description = ticket.Description,
                Status = ticket.Status.ToString(),
                Priority = ticket.Priority.ToString(),
                Category = ticket.Category.ToString(),
                UserId = ticket.UserId,
                UserName = ticket.UserName,
                AssignedToId = ticket.AssignedToId,
                AssignedToName = ticket.AssignedToName,
                CreatedDate = ticket.CreatedDate,
                UpdatedDate = ticket.UpdatedDate,
                ResolvedDate = ticket.ResolvedDate,
                Comments = ticket.Comments.Select(c => new TicketCommentDto
                {
                    Id = c.Id,
                    TicketId = c.TicketId,
                    UserId = c.UserId,
                    UserName = c.UserName,
                    Content = c.Content,
                    IsInternal = c.IsInternal,
                    CreatedDate = c.CreatedDate
                }).ToList(),
                Attachments = ticket.Attachments.Select(a => new TicketAttachmentDto
                {
                    Id = a.Id,
                    TicketId = a.TicketId,
                    FileName = a.FileName,
                    FilePath = a.FilePath,
                    FileSize = a.FileSize,
                    ContentType = a.ContentType,
                    UploadedDate = a.UploadedDate
                }).ToList()
            };
        }

        public async Task<TicketDto> CreateTicketAsync(CreateTicketDto createTicketDto, int userId, string userName)
        {
            // Parse enums
            if (!Enum.TryParse<TicketPriority>(createTicketDto.Priority, out var priority))
                priority = TicketPriority.Medium;

            if (!Enum.TryParse<TicketCategory>(createTicketDto.Category, out var category))
                category = TicketCategory.Other;

            var ticket = new Ticket
            {
                Title = createTicketDto.Title,
                Description = createTicketDto.Description,
                Priority = priority,
                Category = category,
                UserId = userId,
                UserName = userName,
                Status = TicketStatus.Pending,
                CreatedDate = DateTime.UtcNow
            };

            _context.Tickets.Add(ticket);
            await _context.SaveChangesAsync();

            return new TicketDto
            {
                Id = ticket.Id,
                Title = ticket.Title,
                Description = ticket.Description,
                Status = ticket.Status.ToString(),
                Priority = ticket.Priority.ToString(),
                Category = ticket.Category.ToString(),
                UserId = ticket.UserId,
                UserName = ticket.UserName,
                CreatedDate = ticket.CreatedDate,
                Comments = new List<TicketCommentDto>(),
                Attachments = new List<TicketAttachmentDto>()
            };
        }

        public async Task<bool> UpdateTicketAsync(int id, UpdateTicketDto updateTicketDto)
        {
            var ticket = await _context.Tickets.FindAsync(id);
            if (ticket == null)
                return false;

            if (!string.IsNullOrEmpty(updateTicketDto.Title))
                ticket.Title = updateTicketDto.Title;

            if (!string.IsNullOrEmpty(updateTicketDto.Description))
                ticket.Description = updateTicketDto.Description;

            if (!string.IsNullOrEmpty(updateTicketDto.Status) && 
                Enum.TryParse<TicketStatus>(updateTicketDto.Status, out var status))
            {
                ticket.Status = status;
                if (status == TicketStatus.Resolved)
                    ticket.ResolvedDate = DateTime.UtcNow;
            }

            if (!string.IsNullOrEmpty(updateTicketDto.Priority) && 
                Enum.TryParse<TicketPriority>(updateTicketDto.Priority, out var priority))
                ticket.Priority = priority;

            if (!string.IsNullOrEmpty(updateTicketDto.Category) && 
                Enum.TryParse<TicketCategory>(updateTicketDto.Category, out var category))
                ticket.Category = category;

            if (updateTicketDto.AssignedToId.HasValue)
            {
                var assignedUser = await _context.Users.FindAsync(updateTicketDto.AssignedToId.Value);
                if (assignedUser != null)
                {
                    ticket.AssignedToId = updateTicketDto.AssignedToId.Value;
                    ticket.AssignedToName = assignedUser.FullName ?? assignedUser.Username;
                }
            }

            ticket.UpdatedDate = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AddCommentAsync(int ticketId, AddCommentDto addCommentDto, int userId, string userName)
        {
            var ticket = await _context.Tickets.FindAsync(ticketId);
            if (ticket == null)
                return false;

            var comment = new TicketComment
            {
                TicketId = ticketId,
                UserId = userId,
                UserName = userName,
                Content = addCommentDto.Content,
                IsInternal = addCommentDto.IsInternal,
                CreatedDate = DateTime.UtcNow
            };

            _context.TicketComments.Add(comment);
            
            // Update ticket's UpdatedDate
            ticket.UpdatedDate = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteTicketAsync(int id)
        {
            var ticket = await _context.Tickets.FindAsync(id);
            if (ticket == null)
                return false;

            _context.Tickets.Remove(ticket);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
